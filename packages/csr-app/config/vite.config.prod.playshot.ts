import { mergeConfig, loadEnv } from 'vite'
import baseConfig from './vite.config.base'
import { createHtmlPlugin } from 'vite-plugin-html'
import configVisualizerPlugin from './plugin/visualizer'
import configArcoResolverPlugin from './plugin/arcoResolver'
import createPWAPlugin from './plugin/pwa'
import { injectFacebookPixelSouthEastAsia } from './plugins/injectFacebookPixel'
import { injectByteDanceAnalytics } from './plugins/injectByteDanceAnalytics'
import { injectAhrefsAnalytics } from './plugins/injectAhrefsAnalytics'
// import { injectGoogleAnalytics } from './plugins/injectGoogleAnalytics' // 已改用 vue-gtag
import { optimizeFontLoading } from './plugins/optimizeFontLoading'
import { createDefaultDynamicApiHostPlugin } from './plugin/dynamicApiHost'
// import { createSentryPlugin, getSentryConfig } from './plugin/sentry'
import { createProductionCompressPlugin } from './plugin/compress'
import { createProdPerformancePlugins } from './plugin/performance'
import createManifestPlugin from './plugin/manifest'
import {
  createAdvancedPreloadPlugin,
  createCriticalPathPlugin,
} from './plugin/advanced-preload'
import {
  createDynamicImportPlugin,
  createBundleOptimizationPlugin,
} from './plugin/module-federation'
import { resolve } from 'path'

// Load environment variables for PlayShot production
const env = loadEnv('prod.playshot', resolve(process.cwd()))

// Get Sentry configuration
// const sentryConfig = getSentryConfig(env)
// const sentryPlugin = createSentryPlugin(sentryConfig)

export default mergeConfig(
  {
    mode: 'prod.southeastAsia',
    base: '/',
    define: {
      // 生产环境变量定义
      __DEV__: false,
      __TEST__: false,
      // 支付提供商调试变量
      __VITE_PAYMENT_PROVIDER__: JSON.stringify(
        env.VITE_PAYMENT_PROVIDER || '',
      ),
      // Stripe 公钥
      __VITE_STRIPE_PUBLIC_KEY__: JSON.stringify(
        env.VITE_STRIPE_PUBLIC_KEY || '',
      ),
    },
    plugins: [
      configVisualizerPlugin(),
      configArcoResolverPlugin(),
      createPWAPlugin(env),
      createManifestPlugin(), // 保留用于生成version.json
      createDefaultDynamicApiHostPlugin(env.VITE_API_HOST),

      // 高级预加载优化
      createAdvancedPreloadPlugin({
        strategy: 'adaptive',
        networkAware: true,
        criticalResources: [
          '/src/main.ts',
          '/src/AppRoot.vue',
          '/src/router/index.ts',
        ],
        routePreload: {
          '/stories': [
            '/src/mobile/views/stories/index.vue',
            '/src/pc/views/stories/index.vue',
          ],
          '/chat': [
            '/src/mobile/views/chat/index.vue',
            '/src/pc/views/chat/index.vue',
          ],
        },
      }),

      // 关键路径优化
      createCriticalPathPlugin(),

      // 动态导入优化
      createDynamicImportPlugin(),

      // Bundle分析优化 - 生产环境不生成报告文件
      createBundleOptimizationPlugin({ enableReport: false }),
      createHtmlPlugin({
        inject: {
          data: {
            title: 'PlayShot.AI - NSFW Character AI CrushOn Chat - Spicy AI',
            description:
              'PlayShot AI is an adult 18+ virtual roleplay platform where you interact with uncensored, lifelike AI characters. Inspired by leading names like CrushOn AI, Spicy AI, and crush No Filter AI, PlayShot AI takes immersive storytelling to the next level with bold Character AI NSFW experiences and customizable sexting chat pron scenarios. Explore your fantasies with intelligent AI designed for deep, unfiltered connections',
            iconUrl:
              env.VITE_ICON_URL ||
              'https://static.playshot.ai/static/images/icon/playshot-icon.png',
          },
        },
      }),
      // 字体加载优化插件 - 减少对LCP的影响
      optimizeFontLoading(),
      // 优化后的第三方脚本插件 - 减少对LCP的影响
      injectByteDanceAnalytics(), // 用户行为统计 - 用户交互后加载
      injectFacebookPixelSouthEastAsia(), // 广告营销 - 延迟更久加载
      // injectGoogleAnalytics(), // 已改用 vue-gtag，在 main.ts 中配置
      injectAhrefsAnalytics(), // 广告营销 - 延迟最久加载
      // Sentry 插件 (如果配置了的话)
      // ...(sentryPlugin ? [sentryPlugin] : []),
      // 压缩插件
      ...createProductionCompressPlugin(),
      // 性能优化插件
      ...createProdPerformancePlugins(),
    ],
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            // UI库分包
            'arco': ['@arco-design/web-vue'],

            // Vue生态系统
            'vue-utils': ['@vueuse/core', 'vue-i18n'],

            // 第三方服务 (Stripe 已移至 shared-payment)

            // 媒体和动画
            'media': ['howler', 'swiper', 'canvas-confetti'],

            // 工具库
            'utils': ['lodash-es', 'dayjs', 'nanoid', 'uuid'],

            // 可视化库
            'visualization': [
              '@antv/x6',
              '@antv/x6-vue-shape',
              'motion',
              'animejs',
            ],
          },
        },
      },
      chunkSizeWarningLimit: 2000,
      // 启用更激进的压缩
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true, // 生产环境移除console
          drop_debugger: true,
          pure_funcs: ['console.log', 'console.info', 'console.debug'],
          passes: 3, // 增加压缩次数
        },
        mangle: {
          safari10: true,
          toplevel: true, // 更激进的变量名混淆
        },
        format: {
          comments: false,
        },
      },
    },
  },
  baseConfig,
)
